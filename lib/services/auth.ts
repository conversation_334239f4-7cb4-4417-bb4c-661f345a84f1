import crypto from "crypto";

import { Role, TokenType } from "@prisma/client";

import { UserService, ServiceUser } from "./user";
import { EmailService } from "./email";

import { prisma } from "@/lib/db";
import { hashPassword, verifyPassword } from "@/lib/auth";
import { sanitizeEmail } from "@/lib/validation";
import {
  InvalidRolesError,
  EmailAlreadyVerifiedError,
  InvalidVerificationTokenError,
  AuthenticationError,
  EmailNotVerifiedError,
  InvalidCredentialsError,
  PasswordMismatchError,
} from "@/lib/errors";

export interface RegisterUserData {
  email: string;
  password: string;
  name?: string;
  roles: Role[];
}

/**
 * Auth Service - Business logic layer for authentication operations
 * Handles registration, login, password management, and role-based authentication flows
 */
export class AuthService {
  /**
   * Register a new user with email verification
   */
  static async registerUser(data: RegisterUserData): Promise<ServiceUser> {
    const { email, password, name, roles } = data;

    // Sanitize email
    const sanitizedEmail = sanitizeEmail(email);

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: sanitizedEmail },
    });

    if (existingUser) {
      throw new AuthenticationError(
        "User with this email already exists, please sign in.",
      );
    }

    // Validate roles
    if (!roles || roles.length === 0) {
      throw new InvalidRolesError("At least one role is required");
    }

    if (roles.length > 2) {
      throw new InvalidRolesError("Maximum two roles allowed");
    }

    // Create user using UserService - now returns data directly
    const user = await UserService.createUser({
      email: sanitizedEmail,
      password,
      name,
      roles,
      emailVerified: false,
    });

    // Create verification token and send email
    await EmailService.sendVerificationEmail(user.id, user.name || undefined);

    return user;
  }

  /**
   * Authenticate user with email and password
   */
  static async loginUser(
    email: string,
    password: string,
  ): Promise<ServiceUser> {
    // Sanitize email
    const sanitizedEmail = sanitizeEmail(email);

    // Find user with password hash
    const user = await prisma.user.findUniqueOrThrow({
      where: { email: sanitizedEmail },
      select: {
        id: true,
        email: true,
        name: true,
        image: true,
        passwordHash: true,
        roles: true,
        emailVerified: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // Check if user has a password (not OAuth-only user)
    if (!user.passwordHash) {
      throw new AuthenticationError(
        "This account was created with a social provider. Please sign in using that method.",
      );
    }

    // Verify password
    const isPasswordValid = await verifyPassword(password, user.passwordHash);

    if (!isPasswordValid) {
      throw new InvalidCredentialsError("Invalid email or password");
    }

    // Check if email is verified
    if (!user.emailVerified) {
      const error = new EmailNotVerifiedError("EMAIL_NOT_VERIFIED");

      // Add additional data for the error handler
      (error as any).userId = user.id;
      (error as any).email = user.email;
      throw error;
    }

    // Return authenticated user
    return {
      id: user.id,
      email: user.email,
      name: user.name,
      image: user.image,
      roles: user.roles,
      emailVerified: user.emailVerified,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  }

  /**
   * Change user password
   */
  static async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string,
  ): Promise<void> {
    // Get user with current password hash
    const user = await prisma.user.findUniqueOrThrow({
      where: { id: userId },
      select: {
        id: true,
        passwordHash: true,
      },
    });

    if (!user.passwordHash) {
      throw new AuthenticationError(
        "This account doesn't have a password. It was created with a social provider.",
      );
    }

    // Verify current password
    const isCurrentPasswordValid = await verifyPassword(
      currentPassword,
      user.passwordHash,
    );

    if (!isCurrentPasswordValid) {
      throw new PasswordMismatchError();
    }

    // Hash new password
    const newPasswordHash = await hashPassword(newPassword);

    // Update password
    await prisma.user.update({
      where: { id: userId },
      data: { passwordHash: newPasswordHash },
    });
  }

  /**
   * Verify email using token
   */
  static async verifyToken(token: string): Promise<void> {
    // Hash the token to find it in the database
    const tokenHash = crypto.createHash("sha256").update(token).digest("hex");

    // Find the token and associated user
    const userToken = await prisma.userToken.findFirst({
      where: {
        tokenHash,
        type: TokenType.EMAIL_VERIFICATION,
        expiresAt: {
          gt: new Date(),
        },
        usedAt: null, // Token hasn't been used yet
      },
      include: {
        user: {
          select: {
            id: true,
            emailVerified: true,
          },
        },
      },
    });

    if (!userToken) {
      throw new InvalidVerificationTokenError();
    }

    if (userToken.user.emailVerified) {
      throw new EmailAlreadyVerifiedError();
    }

    // Mark token as used and update user as verified
    await prisma.$transaction(async (tx) => {
      await tx.userToken.update({
        where: { id: userToken.id },
        data: { usedAt: new Date() },
      });
      await tx.user.update({
        where: { id: userToken.user.id },
        data: { emailVerified: new Date() },
      });
    });
  }

  /**
   * Assign roles to a user
   */
  static async assignUserRoles(
    userId: string,
    roles: Role[],
  ): Promise<ServiceUser> {
    // Validate roles
    if (!roles || roles.length === 0) {
      throw new InvalidRolesError("At least one role is required");
    }

    if (roles.length > 2) {
      throw new InvalidRolesError("Maximum two roles allowed");
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, roles: true },
    });

    if (!existingUser) {
      throw new AuthenticationError("User not found");
    }

    // Check if user already has roles (prevent overwriting existing roles)
    if (existingUser.roles.length > 0) {
      throw new InvalidRolesError(
        "User already has roles assigned. Use update profile to modify roles.",
      );
    }

    // Update user with new roles
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { roles },
      select: {
        id: true,
        email: true,
        name: true,
        image: true,
        roles: true,
        emailVerified: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return updatedUser;
  }

  /**
   * Verify user email with token
   */
  static async verifyUserEmail(token: string): Promise<void> {
    // Use the existing verifyToken method
    await this.verifyToken(token);
  }
}

import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { Decimal } from "@prisma/client/runtime/library";
import { AdStatus, AppStatus, BidType } from "@prisma/client";

import { AnalyticsService } from "./analytics";

import { prisma } from "@/lib/db";
import {
  getUserAppAnalytics,
  getUserAdAnalytics,
  getMonthlyAppAnalytics,
  getMonthlyAdAnalytics,
} from "@/lib/analytics";
import { AnalyticsDataError } from "@/lib/errors";

vi.mock("@/lib/analytics", () => ({
  getUserAppAnalytics: vi.fn(),
  getUserAdAnalytics: vi.fn(),
  getMonthlyAppAnalytics: vi.fn(),
  getMonthlyAdAnalytics: vi.fn(),
}));

describe("AnalyticsService", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("getModelAnalytics", () => {
    const mockApps = [
      {
        id: "app-1",
        name: "App 1",
        appId: "app_1",
        appSecret: "secret_1",
        description: "App 1 Description",
        status: AppStatus.ACTIVE,
        userId: "user-123",
        createdAt: new Date("2024-01-01"),
        updatedAt: new Date("2024-01-01"),
      },
      {
        id: "app-2",
        name: "App 2",
        appId: "app_2",
        appSecret: "secret_2",
        description: "App 2 Description",
        status: AppStatus.ACTIVE,
        userId: "user-123",
        createdAt: new Date("2024-01-02"),
        updatedAt: new Date("2024-01-02"),
      },
    ];

    const mockUserAnalytics = {
      totalImpressions: 1000,
      totalClicks: 50,
      totalRevenue: 125.5,
      apps: [
        {
          id: "app-1",
          name: "App 1",
          impressions: 600,
          clicks: 30,
          revenue: 75.0,
          ctr: 5,
        },
        {
          id: "app-2",
          name: "App 2",
          impressions: 400,
          clicks: 20,
          revenue: 50.5,
          ctr: 5,
        },
      ],
    };

    const mockMonthlyData = [
      {
        month: "2024-01",
        impressions: 500,
        clicks: 25,
        revenue: 62.75,
      },
      {
        month: "2024-02",
        impressions: 500,
        clicks: 25,
        revenue: 62.75,
      },
    ];

    it("should successfully get model analytics", async () => {
      vi.mocked(prisma.app.findMany).mockResolvedValue(mockApps);
      vi.mocked(getUserAppAnalytics).mockResolvedValue(mockUserAnalytics);
      vi.mocked(getMonthlyAppAnalytics).mockResolvedValue(mockMonthlyData);

      const result = await AnalyticsService.getModelAnalytics("user-123");

      expect(prisma.app.findMany).toHaveBeenCalledWith({
        where: { userId: "user-123" },
        select: { id: true, name: true, createdAt: true },
      });
      expect(getUserAppAnalytics).toHaveBeenCalledWith("user-123");
      expect(getMonthlyAppAnalytics).toHaveBeenCalledWith("user-123");
      expect(result).toEqual({
        totalApps: 2,
        totalImpressions: 1000,
        totalClicks: 50,
        totalRevenue: 125.5,
        monthlyData: mockMonthlyData,
        topApps: [
          {
            id: "app-1",
            name: "App 1",
            createdAt: new Date("2024-01-01"),
            impressions: 600,
            clicks: 30,
            revenue: 75.0,
          },
          {
            id: "app-2",
            name: "App 2",
            createdAt: new Date("2024-01-02"),
            impressions: 400,
            clicks: 20,
            revenue: 50.5,
          },
        ],
      });
    });

    it("should limit top apps to 5", async () => {
      const manyApps = Array.from({ length: 10 }, (_, i) => ({
        id: `app-${i + 1}`,
        name: `App ${i + 1}`,
        status: AppStatus.ACTIVE,
        userId: "user-123",
        appId: `app_${i + 1}`,
        appSecret: `secret_${i + 1}`,
        description: `App ${i + 1} Description`,
        createdAt: new Date(),
        updatedAt: new Date(),
      }));

      const manyAppAnalytics = {
        ...mockUserAnalytics,
        apps: Array.from({ length: 10 }, (_, i) => ({
          id: `app-${i + 1}`,
          name: `App ${i + 1}`,
          impressions: 100,
          clicks: 5,
          revenue: 10,
          ctr: 5,
        })),
      };

      vi.mocked(prisma.app.findMany).mockResolvedValue(manyApps);
      vi.mocked(getUserAppAnalytics).mockResolvedValue(manyAppAnalytics);
      vi.mocked(getMonthlyAppAnalytics).mockResolvedValue(mockMonthlyData);

      const result = await AnalyticsService.getModelAnalytics("user-123");

      expect(result.topApps).toHaveLength(5);
    });

    it("should throw AnalyticsDataError when app not found in database", async () => {
      const incompleteApps = [mockApps[0]]; // Missing app-2

      vi.mocked(prisma.app.findMany).mockResolvedValue(incompleteApps);
      vi.mocked(getUserAppAnalytics).mockResolvedValue(mockUserAnalytics);
      vi.mocked(getMonthlyAppAnalytics).mockResolvedValue(mockMonthlyData);

      await expect(
        AnalyticsService.getModelAnalytics("user-123"),
      ).rejects.toThrow(AnalyticsDataError);
    });

    it("should handle empty apps", async () => {
      vi.mocked(prisma.app.findMany).mockResolvedValue([]);
      vi.mocked(getUserAppAnalytics).mockResolvedValue({
        totalImpressions: 0,
        totalClicks: 0,
        totalRevenue: 0,
        apps: [],
      });
      vi.mocked(getMonthlyAppAnalytics).mockResolvedValue([]);

      const result = await AnalyticsService.getModelAnalytics("user-123");

      expect(result).toEqual({
        totalApps: 0,
        totalImpressions: 0,
        totalClicks: 0,
        totalRevenue: 0,
        monthlyData: [],
        topApps: [],
      });
    });
  });

  describe("getAdvertiserAnalytics", () => {
    const mockAds = [
      {
        id: "ad-1",
        name: "Ad 1",
        userId: "user-123",
        status: AdStatus.ACTIVE,
        description: "Ad 1 Description",
        imageUrl: null,
        productUrl: "https://example.com",
        targetTopics: ["tech"],
        budget: new Decimal(100),
        bidType: BidType.CPC,
        bidAmount: new Decimal(0.5),
        createdAt: new Date("2024-01-01"),
        updatedAt: new Date("2024-01-01"),
      },
      {
        id: "ad-2",
        name: "Ad 2",
        userId: "user-123",
        status: AdStatus.ACTIVE,
        description: "Ad 2 Description",
        imageUrl: null,
        productUrl: "https://example.com",
        targetTopics: ["ai"],
        budget: new Decimal(200),
        bidType: BidType.CPM,
        bidAmount: new Decimal(2.0),
        createdAt: new Date("2024-01-02"),
        updatedAt: new Date("2024-01-02"),
      },
    ];

    const mockUserAnalytics = {
      totalSpend: 75.25,
      totalImpressions: 2000,
      totalClicks: 100,
      averageCTR: "5.00",
      ads: [
        {
          id: "ad-1",
          name: "Ad 1",
          budget: new Decimal(100),
          bidType: BidType.CPC,
          bidAmount: new Decimal(0.5),
          createdAt: new Date("2024-01-01"),
          updatedAt: new Date("2024-01-01"),
          impressions: 1200,
          clicks: 60,
          spend: 45.0,
          ctr: 5,
        },
        {
          id: "ad-2",
          name: "Ad 2",
          budget: new Decimal(200),
          bidType: BidType.CPM,
          bidAmount: new Decimal(2.0),
          createdAt: new Date("2024-01-02"),
          updatedAt: new Date("2024-01-02"),
          impressions: 800,
          clicks: 40,
          spend: 30.25,
          ctr: 5,
        },
      ],
    };

    const mockMonthlyData = [
      {
        month: "2024-01",
        impressions: 1000,
        clicks: 50,
        spend: 37.625,
      },
      {
        month: "2024-02",
        impressions: 1000,
        clicks: 50,
        spend: 37.625,
      },
    ];

    it("should successfully get advertiser analytics", async () => {
      vi.mocked(prisma.advertisement.findMany).mockResolvedValue(mockAds);
      vi.mocked(getUserAdAnalytics).mockResolvedValue(mockUserAnalytics);
      vi.mocked(getMonthlyAdAnalytics).mockResolvedValue(mockMonthlyData);

      const result = await AnalyticsService.getAdvertiserAnalytics("user-123");

      expect(prisma.advertisement.findMany).toHaveBeenCalledWith({
        where: { userId: "user-123" },
        select: {
          id: true,
          name: true,
          budget: true,
          bidType: true,
          bidAmount: true,
          createdAt: true,
        },
      });
      expect(getUserAdAnalytics).toHaveBeenCalledWith("user-123");
      expect(getMonthlyAdAnalytics).toHaveBeenCalledWith("user-123");
      expect(result).toEqual({
        totalCampaigns: 2,
        totalBudget: 300,
        totalSpend: 75.25,
        totalImpressions: 2000,
        totalClicks: 100,
        averageCTR: "5.00",
        monthlyData: mockMonthlyData,
        topCampaigns: [
          {
            id: "ad-1",
            name: "Ad 1",
            budget: 100,
            bidType: "CPC",
            bidAmount: 0.5,
            createdAt: new Date("2024-01-01"),
            impressions: 1200,
            clicks: 60,
            spend: 45.0,
            ctr: 5.0,
          },
          {
            id: "ad-2",
            name: "Ad 2",
            budget: 200,
            bidType: "CPM",
            bidAmount: 2.0,
            createdAt: new Date("2024-01-02"),
            impressions: 800,
            clicks: 40,
            spend: 30.25,
            ctr: 5.0,
          },
        ],
      });
    });

    it("should limit top campaigns to 5", async () => {
      const manyAds = Array.from({ length: 10 }, (_, i) => ({
        id: `ad-${i + 1}`,
        name: `Ad ${i + 1}`,
        userId: "user-123",
        status: AdStatus.ACTIVE,
        description: `Ad ${i + 1} Description`,
        imageUrl: null,
        productUrl: "https://example.com",
        targetTopics: ["tech"],
        budget: new Decimal(100),
        bidType: BidType.CPC,
        bidAmount: new Decimal(0.5),
        createdAt: new Date(),
        updatedAt: new Date(),
      }));

      const manyAdAnalytics = {
        ...mockUserAnalytics,
        ads: Array.from({ length: 10 }, (_, i) => ({
          id: `ad-${i + 1}`,
          name: `Ad ${i + 1}`,
          budget: new Decimal(100),
          bidType: BidType.CPC,
          bidAmount: new Decimal(0.5),
          createdAt: new Date(),
          updatedAt: new Date(),
          impressions: 200,
          clicks: 10,
          spend: 5,
          ctr: 5,
        })),
      };

      vi.mocked(prisma.advertisement.findMany).mockResolvedValue(manyAds);
      vi.mocked(getUserAdAnalytics).mockResolvedValue(manyAdAnalytics);
      vi.mocked(getMonthlyAdAnalytics).mockResolvedValue(mockMonthlyData);

      const result = await AnalyticsService.getAdvertiserAnalytics("user-123");

      expect(result.topCampaigns).toHaveLength(5);
    });

    it("should throw AnalyticsDataError when ad not found in database", async () => {
      const incompleteAds = [mockAds[0]]; // Missing ad-2

      vi.mocked(prisma.advertisement.findMany).mockResolvedValue(incompleteAds);
      vi.mocked(getUserAdAnalytics).mockResolvedValue(mockUserAnalytics);
      vi.mocked(getMonthlyAdAnalytics).mockResolvedValue(mockMonthlyData);

      await expect(
        AnalyticsService.getAdvertiserAnalytics("user-123"),
      ).rejects.toThrow(AnalyticsDataError);
    });

    it("should handle empty ads", async () => {
      vi.mocked(prisma.advertisement.findMany).mockResolvedValue([]);
      vi.mocked(getUserAdAnalytics).mockResolvedValue({
        totalSpend: 0,
        totalImpressions: 0,
        totalClicks: 0,
        averageCTR: "0.00",
        ads: [],
      });
      vi.mocked(getMonthlyAdAnalytics).mockResolvedValue([]);

      const result = await AnalyticsService.getAdvertiserAnalytics("user-123");

      expect(result).toEqual({
        totalCampaigns: 0,
        totalBudget: 0,
        totalSpend: 0,
        totalImpressions: 0,
        totalClicks: 0,
        averageCTR: "0.00",
        monthlyData: [],
        topCampaigns: [],
      });
    });
  });
});

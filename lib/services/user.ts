import { Role } from "@prisma/client";

import { prisma } from "@/lib/db";
import { hashPassword } from "@/lib/auth";
import { sanitizeEmail } from "@/lib/validation";
import {
  UserAlreadyExistsError,
  EmailAlreadyTakenError,
  InvalidRolesError,
} from "@/lib/errors";

export interface CreateUserData {
  email: string;
  password?: string;
  name?: string;
  roles: Role[];
  emailVerified?: boolean;
}

export interface UpdateUserData {
  name?: string;
  email?: string;
  roles?: Role[];
  image?: string;
}

// Type for the user object returned by our service methods (subset of Prisma User)
export type ServiceUser = {
  id: string;
  email: string;
  name: string | null;
  image: string | null;
  roles: Role[];
  emailVerified: Date | null;
  createdAt: Date;
  updatedAt: Date;
};

/**
 * User Service - Business logic layer for user management operations
 * Handles user creation, updates, role management, and profile operations
 */
export class UserService {
  /**
   * Create a new user
   */
  static async createUser(data: CreateUserData): Promise<ServiceUser> {
    const { email, password, name, roles, emailVerified = false } = data;

    // Sanitize email
    const sanitizedEmail = sanitizeEmail(email);

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: sanitizedEmail },
    });

    if (existingUser) {
      throw new UserAlreadyExistsError();
    }

    // Hash password if provided
    let passwordHash: string | undefined;

    if (password) {
      passwordHash = await hashPassword(password);
    }

    // Create user
    const user = await prisma.user.create({
      data: {
        email: sanitizedEmail,
        passwordHash,
        name: name || null,
        roles,
        emailVerified: emailVerified ? new Date() : null,
      },
      select: {
        id: true,
        email: true,
        name: true,
        image: true,
        roles: true,
        emailVerified: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return user;
  }

  /**
   * Get user by ID
   */
  static async getUserById(userId: string): Promise<ServiceUser> {
    const user = await prisma.user.findUniqueOrThrow({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        image: true,
        roles: true,
        emailVerified: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return user;
  }

  /**
   * Get user by email
   */
  static async getUserByEmail(email: string): Promise<ServiceUser> {
    const sanitizedEmail = sanitizeEmail(email);

    const user = await prisma.user.findUniqueOrThrow({
      where: { email: sanitizedEmail },
      select: {
        id: true,
        email: true,
        name: true,
        image: true,
        roles: true,
        emailVerified: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return user;
  }

  /**
   * Update user information (handles profile updates, role assignments, etc.)
   */
  static async updateUser(
    userId: string,
    data: UpdateUserData,
  ): Promise<ServiceUser> {
    const { name, email, roles, image } = data;

    // Prepare update data
    const updateData: any = {};

    if (name !== undefined) updateData.name = name;
    if (image !== undefined) updateData.image = image;

    // Handle roles update with validation
    if (roles !== undefined) {
      // Validate roles
      const validRoles = roles.filter((role) =>
        Object.values(Role).includes(role),
      );

      if (validRoles.length === 0) {
        throw new InvalidRolesError("At least one valid role is required");
      }

      if (validRoles.length > 2) {
        throw new InvalidRolesError("Maximum two roles allowed");
      }

      updateData.roles = validRoles;
    }

    // Handle email update with sanitization
    if (email !== undefined) {
      const sanitizedEmail = sanitizeEmail(email);

      // Check if new email is already taken by another user
      const existingUser = await prisma.user.findFirst({
        where: {
          email: sanitizedEmail,
          NOT: { id: userId },
        },
      });

      if (existingUser) {
        throw new EmailAlreadyTakenError();
      }

      updateData.email = sanitizedEmail;
      // Reset email verification if email is changed
      updateData.emailVerified = null;
    }

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updateData,
      select: {
        id: true,
        email: true,
        name: true,
        image: true,
        roles: true,
        emailVerified: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return updatedUser;
  }

  /**
   * Delete user
   */
  static async deleteUser(userId: string): Promise<void> {
    await prisma.user.delete({
      where: { id: userId },
    });
  }
}

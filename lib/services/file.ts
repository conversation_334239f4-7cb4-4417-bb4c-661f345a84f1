import { writeFile } from "fs/promises";
import { join } from "path";

import { nanoid } from "nanoid";

export interface UploadFileData {
  file: File;
  userId?: string; // Optional for authenticated uploads
}

export interface UploadedFile {
  url: string;
  filename: string;
  size: number;
  type: string;
  message: string;
}

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_TYPES = [
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/webp",
  "image/gif",
];

/**
 * File Service - Business logic layer for file upload operations
 * Handles file validation, storage, and metadata management
 */
export class FileService {
  /**
   * Upload a file to the server
   */
  static async uploadFile(data: UploadFileData): Promise<UploadedFile> {
    const { file, userId } = data;

    // Validate file type
    if (!ALLOWED_TYPES.includes(file.type)) {
      throw new Error(
        "Invalid file type. Only JPEG, PNG, WebP, and GIF are allowed.",
      );
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      throw new Error("File too large. Maximum size is 5MB.");
    }

    // Generate unique filename
    const fileExtension = file.name.split(".").pop();
    const uniqueFilename = `${nanoid(16)}.${fileExtension}`;

    // Convert file to buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Save file to public/uploads directory
    const uploadPath = join(process.cwd(), "public", "uploads", uniqueFilename);

    await writeFile(uploadPath, buffer);

    // Return the public URL
    const fileUrl = `/uploads/${uniqueFilename}`;

    return {
      message: "File uploaded successfully",
      url: fileUrl,
      filename: uniqueFilename,
      size: file.size,
      type: file.type,
    };
  }

  /**
   * Get allowed file types
   */
  static getAllowedTypes(): string[] {
    return [...ALLOWED_TYPES];
  }

  /**
   * Get maximum file size
   */
  static getMaxFileSize(): number {
    return MAX_FILE_SIZE;
  }
}

import { z } from "zod/v4";

// User registration validation
export const registerSchema = z.object({
  email: z.email("Invalid email format"),
  password: z.string().min(8, "Password must be at least 8 characters long"),
  roles: z
    .array(z.enum(["MODEL_PROVIDER", "ADVERTISER"]))
    .optional()
    .default([]), // Roles are optional during registration, default to empty array
});

// Email verification validation
export const emailVerificationSchema = z.object({
  token: z.string().min(1, "Verification token is required"),
});

// Resend verification email validation
export const resendVerificationSchema = z.object({
  email: z.email("Invalid email format"),
});

// App registration validation
export const appSchema = z.object({
  name: z
    .string({ error: "App name is required" })
    .min(3, "App name must be at least 3 characters long")
    .max(100, "App name too long"),
  description: z.string().max(500, "Description too long").optional(),
});

// Advertisement creation validation
export const advertisementSchema = z.object({
  name: z
    .string({ error: "Campaign name is required" })
    .max(100, "Campaign name too long"),
  description: z
    .string({ error: "Description is required" })
    .max(500, "Description too long"),
  productUrl: z.url({ error: "Invalid product URL" }),
  imageUrl: z.url({ error: "Invalid image URL" }).optional().nullable(),
  targetTopics: z.array(z.string()).max(10, "Too many target topics"),
  budget: z
    .number()
    .min(1, "Budget must be at least $1")
    .max(100000, "Budget too high"),
  bidType: z.enum(["CPC", "CPM"]),
  bidAmount: z
    .number()
    .min(0.01, "Bid amount must be at least $0.01")
    .max(100, "Bid amount too high"),
});

// Ad serving validation
export const adServeSchema = z.object({
  appId: z.string({ error: "App ID is required" }),
  appSecret: z.string({ error: "App secret is required" }),
  topics: z.array(z.string()).optional(),
  userContext: z
    .object({
      userAgent: z.string().optional(),
      language: z.string().optional(),
      country: z.string().optional(),
    })
    .optional(),
});

// Impression tracking validation
export const impressionSchema = z.object({
  adId: z.string({ error: "Ad ID is required" }),
  appId: z.string({ error: "App ID is required" }),
  clicked: z.boolean().default(false),
  userAgent: z.string().optional(),
});

// File upload validation
export const fileUploadSchema = z.object({
  file: z.object({
    name: z.string().min(1, "File name is required"),
    size: z.number().max(5 * 1024 * 1024, "File too large (max 5MB)"),
    type: z
      .string()
      .refine(
        (type) =>
          [
            "image/jpeg",
            "image/jpg",
            "image/png",
            "image/webp",
            "image/gif",
          ].includes(type),
        "Invalid file type. Only JPEG, PNG, WebP, and GIF are allowed.",
      ),
  }),
});

// Serve ad request validation
export const serveAdSchema = z.object({
  appId: z.string().min(1, "App ID is required"),
  appSecret: z.string().min(1, "App secret is required"),
  topics: z.array(z.string()).optional(),
  userContext: z
    .object({
      userAgent: z.string().optional(),
      language: z.string().optional(),
    })
    .optional(),
});

// Enhanced impression tracking validation
export const impressionTrackingSchema = z.object({
  adId: z.string().min(1, "Ad ID is required"),
  appId: z.string().min(1, "App ID is required"),
  clicked: z.boolean().default(false),
  userAgent: z.string().optional(),
});

// Role assignment validation (enhanced)
export const assignRolesSchema = z.object({
  roles: z
    .array(z.enum(["MODEL_PROVIDER", "ADVERTISER"]))
    .min(1, "At least one role is required")
    .max(2, "Maximum two roles allowed")
    .refine(
      (roles) => new Set(roles).size === roles.length,
      "Duplicate roles are not allowed",
    ),
});

// Validation helper function
export function validateData<T>(
  schema: z.ZodSchema<T>,
  data: unknown,
): { success: true; data: T } | { success: false; error: string } {
  const result = schema.safeParse(data);

  if (result.success) {
    return { success: true, data: result.data };
  }

  return { success: false, error: result.error.issues[0].message };
}

// Sanitization helpers
export function sanitizeString(input: string): string {
  return input.trim().replace(/[<>]/g, "");
}

export function sanitizeEmail(email: string): string {
  return email.toLowerCase().trim();
}

// Rate limiting helpers
export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
}

export const rateLimitConfigs = {
  auth: { windowMs: 15 * 60 * 1000, maxRequests: 1000 }, // 5 requests per 15 minutes
  api: { windowMs: 60 * 1000, maxRequests: 100 }, // 100 requests per minute
  upload: { windowMs: 60 * 1000, maxRequests: 10 }, // 10 uploads per minute
  serve: { windowMs: 60 * 1000, maxRequests: 1000 }, // 1000 ad serves per minute
};

// Simple in-memory rate limiter (for production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(
  key: string,
  config: RateLimitConfig,
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const record = rateLimitStore.get(key);

  if (!record || now > record.resetTime) {
    // Reset or create new record
    const resetTime = now + config.windowMs;

    rateLimitStore.set(key, { count: 1, resetTime });

    return { allowed: true, remaining: config.maxRequests - 1, resetTime };
  }

  if (record.count >= config.maxRequests) {
    return { allowed: false, remaining: 0, resetTime: record.resetTime };
  }

  record.count++;

  return {
    allowed: true,
    remaining: config.maxRequests - record.count,
    resetTime: record.resetTime,
  };
}

// Clean up expired rate limit records
setInterval(() => {
  const now = Date.now();

  rateLimitStore.forEach((record, key) => {
    if (now > record.resetTime) {
      rateLimitStore.delete(key);
    }
  });
}, 60 * 1000); // Clean up every minute

"use client";

import { But<PERSON> } from "@heroui/button";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { Chip } from "@heroui/chip";
import NextLink from "next/link";
import clsx from "clsx";

import { title, subtitle } from "@/components/primitives";

export default function Home() {
  return (
    <div className="flex flex-col gap-24 py-16">
      {/* Hero Section */}
      <section className="relative flex flex-col items-center text-center px-4 md:px-0">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-transparent to-secondary/20 pointer-events-none blur-xl" />
        <div className="relative z-10 max-w-4xl">
          <h1 className={clsx(title({ size: "lg" }), "mt-6 leading-snug")}>
            Bring Free AI to Everyone with&nbsp;
            <span className={title({ color: "violet", size: "lg" })}>
              Contextual Advertising
            </span>
          </h1>
          <p className={subtitle({ class: "mt-6 text-lg text-default-600" })}>
            The premier platform for AI model providers to monetize their
            applications and advertisers to reach engaged AI users with
            precision targeting.
          </p>
          <div className="mt-10 flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              as={NextLink}
              className="font-semibold transition-transform hover:-translate-y-1"
              color="primary"
              href="/register"
              size="lg"
            >
              Get Started Free
            </Button>
            <Button
              as={NextLink}
              className="font-semibold transition-opacity hover:opacity-80"
              href="/login"
              size="lg"
              variant="bordered"
            >
              Sign In
            </Button>
          </div>
        </div>
      </section>

      {/* Introduction Section - Mindify Search and In Development Notice */}
      <section className="max-w-screen-md mx-auto px-4 text-center">
        <div className="mb-12">
          <h2 className={title({ size: "md" })}>
            Welcome to the <br />{" "}
            <h2 className="text-warning">the Age of Intelligence</h2>
          </h2>
          <p className={subtitle({ class: "mt-4 text-default-600" })}>
            A powerful AI search engine that helps you find the best AI models
            and applications that you can use for free, powered by contextual
            advertising.
          </p>
        </div>
        <div className="bg-default-100 p-6 rounded-lg shadow-md">
          <p className="text-default-700">
            Note: Both Mindify Search and AiD platform are currently in
            development. Stay tuned for updates!
          </p>
        </div>
      </section>

      {/* Features Section */}
      <section className="max-w-screen-lg mx-auto px-4" id="features">
        <div className="text-center mb-12">
          <h2 className={title({ size: "md" })}>
            Powerful Features for Both Sides
          </h2>
          <p className={subtitle({ class: "mt-4 text-default-600" })}>
            Everything you need to succeed in AI advertising
          </p>
        </div>
        <div className="grid gap-8 md:grid-cols-2">
          {[
            {
              key: "providers",
              label: "For Providers",
              color: "primary",
              title: "Monetize Your AI Apps",
              items: [
                "Easy integration with secure API credentials",
                "Real-time revenue analytics and reporting",
                "Competitive CPM and CPC rates",
                "Automated ad serving and optimization",
              ],
            },
            {
              key: "advertisers",
              label: "For Advertisers",
              color: "secondary",
              title: "Reach AI Users",
              items: [
                "Target specific AI application categories",
                "Detailed performance metrics and ROI tracking",
                "Flexible budget management and bidding",
                "High-quality, engaged AI user audience",
              ],
            },
          ].map((section) => (
            <Card
              key={section.key}
              className="p-6 border-default-200 hover:shadow-lg hover:border-transparent transition-all"
            >
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3">
                  <Chip color="default" variant="flat">
                    {section.label}
                  </Chip>
                  <h3 className="text-xl font-bold text-foreground">
                    {section.title}
                  </h3>
                </div>
              </CardHeader>
              <CardBody className="pt-2">
                <ul className="space-y-3 text-default-600">
                  {section.items.map((item) => (
                    <li key={item} className="flex items-start gap-2">
                      <span className={clsx("mt-0.5 text-", section.color)}>
                        ✓
                      </span>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </CardBody>
            </Card>
          ))}
        </div>
      </section>

      {/* Context-Aware AI Ads Section */}
      <section className="max-w-screen-lg mx-auto px-4" id="context-aware-ads">
        <div className="text-center mb-12">
          <h2 className={title({ size: "md" })}>Context-Aware AI Ads</h2>
          <p className={subtitle({ class: "mt-4 text-default-600" })}>
            Delivering the right message at the right time—our AI analyzes user
            context to serve personalized ads that enhance engagement and
            conversion.
          </p>
        </div>
        <div className="grid gap-8 md:grid-cols-3">
          <Card className="p-6 border-default-200 hover:shadow-lg hover:border-transparent transition-all text-center">
            <CardHeader className="pb-4">
              <h3 className="text-xl font-bold">Dynamic Content</h3>
            </CardHeader>
            <CardBody className="pt-2">
              <p>
                Adjust ad creatives based on user interests and session data,
                ensuring relevance and resonance.
              </p>
            </CardBody>
          </Card>
          <Card className="p-6 border-default-200 hover:shadow-lg hover:border-transparent transition-all text-center">
            <CardHeader className="pb-4">
              <h3 className="text-xl font-bold">Real-Time Targeting</h3>
            </CardHeader>
            <CardBody className="pt-2">
              <p>
                Leverage in-app behavior to trigger context-specific offers,
                driving higher engagement rates.
              </p>
            </CardBody>
          </Card>
          <Card className="p-6 border-default-200 hover:shadow-lg hover:border.transparent transition-all text-center">
            <CardHeader className="pb-4">
              <h3 className="text-xl font-bold">Seamless Integration</h3>
            </CardHeader>
            <CardBody className="pt-2">
              <p>
                Easily integrate our AI ad SDK into your applications with
                minimal configuration.
              </p>
            </CardBody>
          </Card>
        </div>
      </section>

      {/* Example: Context-Aware Prompt & Response */}
      <section
        className="max-w-screen-lg mx-auto px-4"
        id="example-prompt-response"
      >
        <div className="text-center mb-12">
          <h2 className={title({ size: "md" })}>
            Context-Aware AI Prompt & Response
          </h2>
          <p className={subtitle({ class: "mt-4 text-default-600" })}>
            An AI-powered assistance example with a smoothly integrated ad.
          </p>
        </div>

        {/* Prompt Card */}
        <Card className="p-6 border-default-200 hover:shadow-lg hover:border-transparent transition-all">
          <CardHeader className="pb-4">
            <h3 className="text-lg font-bold">🧠 Prompt (User Context)</h3>
          </CardHeader>
          <CardBody className="pt-2">
            <pre className="whitespace-pre-wrap text-default-700">{`Hey, I’m trying to debug a memory leak in my React app. It happens when switching pages with \`useEffect\` hooks that listen to \`window\` events. Any suggestions on how to fix it?`}</pre>
          </CardBody>
        </Card>

        {/* Response Card */}
        <Card className="p-6 border-default-200 hover:shadow-lg hover:border-transparent transition-all mt-6">
          <CardHeader className="pb-4">
            <h3 className="text-lg font-bold">
              🤖 AI Response (Context-Aware + Ad)
            </h3>
          </CardHeader>
          <CardBody className="pt-2 space-y-4">
            <pre className="bg-default-100 p-4 rounded whitespace-pre-wrap text-default-700">{`useEffect(() => {
  const handleResize = () => {
    console.log("Window resized");
  };

  window.addEventListener("resize", handleResize);

  return () => {
    // Cleanup to prevent memory leaks
    window.removeEventListener("resize", handleResize);
  };
}, []);`}</pre>
            <p>
              By ensuring the cleanup function removes the event listener, you
              prevent memory buildup when navigating between pages.
            </p>
            <div className="bg-default-50 p-4 rounded border border-default-200">
              <p className="font-medium">
                📢 <span className="font-bold">Dev Tip:</span> Want to{" "}
                <i>automatically detect memory leaks</i> in your React apps? Try{" "}
                <strong>DebugSight</strong>, the AI-powered performance profiler
                that integrates directly with your dev tools.
              </p>
              <p>
                Get 20% off with the code <code>FIXMEM20</code> –{" "}
                <a
                  className="text-primary underline"
                  href="https://www.debugsight.ai"
                >
                  Start debugging smarter →
                </a>
              </p>
            </div>
          </CardBody>
        </Card>
      </section>

      {/* CTA Section */}
      <section className="relative text-center px-4">
        <div className="absolute inset-0  rounded-2xl transform rotate-1 pointer-events-none" />
        <div className="relative z-10 max-w-2xl mx-auto py-12 px-6 backdrop-blur bg-default-0/60 rounded-2xl shadow-lg">
          <h2 className={title({ size: "md" })}>Ready to Get Started?</h2>
          <p className={subtitle({ class: "mt-4 mb-8 text-default-600" })}>
            Join thousands of AI providers and advertisers already using our
            platform
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              as={NextLink}
              className="font-semibold transition-transform hover:-translate-y-1"
              color="primary"
              href="/register?role=provider"
              size="lg"
            >
              Start as Provider
            </Button>
            <Button
              as={NextLink}
              className="font-semibold transition-transform hover:-translate-y-1"
              color="secondary"
              href="/register?role=advertiser"
              size="lg"
            >
              Start as Advertiser
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}

// app/api/analytics/[role]/route.ts

// 1️⃣ Prevent any static prerender/analysis—this runs at request time
export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";

import { authOptions } from "@/lib/auth";
import { AnalyticsService } from "@/lib/services/analytics";

// ── Shape of your analytics SDK’s ads array (ctr is a string) ─────────

export async function GET(request: NextRequest) {
  try {
    // ── 1. AUTH ─────────────────────────────────────────────────────────
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // ── 2. EXTRACT & VALIDATE [role] ───────────────────────────────────
    const segments = request.nextUrl.pathname.split("/");
    const role = segments[segments.length - 1] as "model" | "advertiser";

    // Use AnalyticsService to get role-based analytics
    const analyticsResult = await AnalyticsService.getRoleAnalytics({
      userId: session.user.id,
      role,
      userRoles: session.user.roles,
    });

    if (!analyticsResult.success) {
      const statusCode = analyticsResult.error?.includes("Invalid role")
        ? 400
        : analyticsResult.error?.includes("required role")
          ? 403
          : 500;

      return NextResponse.json(
        { error: analyticsResult.error },
        { status: statusCode },
      );
    }

    return NextResponse.json(analyticsResult.data);
  } catch (error) {
    console.error("Analytics fetch error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

// app/api/analytics/[role]/route.ts

// 1️⃣ Prevent any static prerender/analysis—this runs at request time
export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";

import { authOptions } from "@/lib/auth";
import { AnalyticsService } from "@/lib/services/analytics";
import { withApiErrorHandling } from "@/lib/error-handler";

// ── Shape of your analytics SDK’s ads array (ctr is a string) ─────────

async function getAnalyticsHandler(request: NextRequest): Promise<NextResponse> {
  // Check authentication
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Extract and validate [role] parameter
  const segments = request.nextUrl.pathname.split("/");
  const role = segments[segments.length - 1] as "model" | "advertiser";

  // Use AnalyticsService to get role-based analytics
  const analytics = await AnalyticsService.getRoleAnalytics({
    userId: session.user.id,
    role,
    userRoles: session.user.roles,
  });

  return NextResponse.json(analytics);
}

// Apply error handling middleware
export const GET = withApiErrorHandling(getAnalyticsHandler);

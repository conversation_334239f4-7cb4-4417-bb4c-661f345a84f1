import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";

import { authOptions } from "@/lib/auth";
import { appSchema, validateData } from "@/lib/validation";
import { AppService } from "@/lib/services/app";

export async function GET(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user.roles.includes("MODEL_PROVIDER")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Use AppService to get user apps
    const result = await AppService.getUserApps(session.user.id);

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    return NextResponse.json({ apps: result.data.apps });
  } catch (error) {
    console.error("Apps fetch error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user.roles.includes("MODEL_PROVIDER")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();

    // Validate input using Zod schema
    const validation = validateData(appSchema, body);

    if (!validation.success) {
      return NextResponse.json({ error: validation.error }, { status: 400 });
    }

    const { name, description } = validation.data;

    // Use AppService to create app
    const result = await AppService.createApp({
      userId: session.user.id,
      name,
      description,
    });

    if (!result.success) {
      const statusCode = result.error?.includes("MODEL_PROVIDER") ? 403 : 500;

      return NextResponse.json({ error: result.error }, { status: statusCode });
    }

    return NextResponse.json(
      {
        message: result.data.message,
        app: result.data.app,
      },
      { status: 201 },
    );
  } catch (error) {
    console.error("App creation error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

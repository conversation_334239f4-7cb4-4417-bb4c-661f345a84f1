import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";

import { authOptions } from "@/lib/auth";
import { appSchema, validateData } from "@/lib/validation";
import { AppService } from "@/lib/services/app";
import { withApiErrorHandling } from "@/lib/error-handler";

async function getAppsHandler(_request: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  if (!session || !session.user.roles.includes("MODEL_PROVIDER")) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Use AppService to get user apps
  const apps = await AppService.getUserApps(session.user.id);

  return NextResponse.json({ apps });
}

// Apply error handling middleware
export const GET = withApiErrorHandling(getAppsHandler);

async function createAppHandler(request: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  if (!session || !session.user.roles.includes("MODEL_PROVIDER")) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const body = await request.json();

  // Validate input using Zod schema
  const validation = validateData(appSchema, body);

  if (!validation.success) {
    return NextResponse.json({ error: validation.error }, { status: 400 });
  }

  const { name, description } = validation.data;

  // Use AppService to create app
  const app = await AppService.createApp(session.user.id, name, description);

  return NextResponse.json(
    {
      message: "Application created successfully",
      app,
    },
    { status: 201 },
  );
}

// Apply error handling middleware
export const POST = withApiErrorHandling(createAppHandler);

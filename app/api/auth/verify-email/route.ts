import { NextRequest, NextResponse } from "next/server";

import {
  emailVerificationSchema,
  validateData,
  rateLimitConfigs,
} from "@/lib/validation";
import {
  withMiddleware,
  withSecurityHeaders,
  withRateLimit,
} from "@/lib/middleware";
import { AuthService } from "@/lib/services/auth";
import { withApiErrorHandling } from "@/lib/error-handler";

async function verifyEmailHandler(request: NextRequest): Promise<NextResponse> {
  const body = await request.json();

  // Validate input using Zod schema
  const validation = validateData(emailVerificationSchema, body);

  if (!validation.success) {
    return NextResponse.json({ error: validation.error }, { status: 400 });
  }

  const { token } = validation.data;

  // Use AuthService to verify email
  await AuthService.verifyUserEmail(token);

  return NextResponse.json(
    {
      message: "Email verified successfully",
    },
    { status: 200 },
  );
}

// Apply middleware with error handling
export const POST = withMiddleware(
  withSecurityHeaders(),
  withRateLimit(rateLimitConfigs.auth),
)(withApiErrorHandling(verifyEmailHandler));

// GET endpoint for URL-based verification (when user clicks link)
async function verifyEmailGetHandler(
  request: NextRequest,
): Promise<NextResponse> {
  const { searchParams } = new URL(request.url);
  const token = searchParams.get("token");

  if (!token) {
    return NextResponse.json(
      { error: "Verification token is required" },
      { status: 400 },
    );
  }

  // Use AuthService to verify email
  await AuthService.verifyUserEmail(token);

  return NextResponse.json(
    {
      message: "Email verified successfully",
    },
    { status: 200 },
  );
}

export const GET = withMiddleware(
  withSecurityHeaders(),
  withRateLimit(rateLimitConfigs.auth),
)(withApiErrorHandling(verifyEmailGetHandler));

import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { Role } from "@prisma/client";

import { authOptions } from "@/lib/auth";
import {
  withMiddleware,
  withSecurityHeaders,
  withRateLimit,
} from "@/lib/middleware";
import {
  assignRolesSchema,
  validateData,
  rateLimitConfigs,
} from "@/lib/validation";
import { AuthService } from "@/lib/services/auth";
import { withApiErrorHandling } from "@/lib/error-handler";

async function assignRolesHandler(request: NextRequest): Promise<NextResponse> {
  // Check authentication
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json(
      { error: "Authentication required" },
      { status: 401 },
    );
  }

  // Parse and validate request body
  const body = await request.json();
  const validation = validateData(assignRolesSchema, body);

  if (!validation.success) {
    return NextResponse.json(
      {
        error: "Invalid input",
        details: validation.error,
      },
      { status: 400 },
    );
  }

  const { roles } = validation.data;

  // Use AuthService to assign roles
  const user = await AuthService.assignUserRoles(
    session.user.id,
    roles as Role[],
  );

  return NextResponse.json(
    {
      message: "Roles assigned successfully",
      user,
    },
    { status: 200 },
  );
}

// Apply middleware with error handling
export const POST = withMiddleware(
  withSecurityHeaders(),
  withRateLimit(rateLimitConfigs.auth),
)(withApiErrorHandling(assignRolesHandler));

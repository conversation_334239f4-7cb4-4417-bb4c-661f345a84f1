import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { z } from "zod/v4";
import { Role } from "@prisma/client";

import { authOptions } from "@/lib/auth";
import {
  withMiddleware,
  withSecurityHeaders,
  withRateLimit,
} from "@/lib/middleware";
import { rateLimitConfigs } from "@/lib/validation";
import { AuthService } from "@/lib/services/auth";

// Validation schema
const assignRolesSchema = z.object({
  roles: z
    .array(z.enum(["MODEL_PROVIDER", "ADVERTISER"]))
    .min(1, "At least one role is required")
    .max(2, "Maximum two roles allowed"),
});

async function assignRolesHandler(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = assignRolesSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: "Invalid input",
          details: validationResult.error.message,
        },
        { status: 400 },
      );
    }

    const { roles } = validationResult.data;

    // Use AuthService to assign roles
    const result = await AuthService.assignUserRoles(
      session.user.id,
      roles as Role[],
    );

    if (!result.success) {
      const statusCode = result.error?.includes("not found")
        ? 404
        : result.error?.includes("already")
          ? 409
          : 400;

      return NextResponse.json({ error: result.error }, { status: statusCode });
    }

    return NextResponse.json(
      {
        message: result.data.message,
        user: result.data.user,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error("Role assignment error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

// Apply middleware
export const POST = withMiddleware(
  withSecurityHeaders(),
  withRateLimit(rateLimitConfigs.auth),
)(assignRolesHandler);

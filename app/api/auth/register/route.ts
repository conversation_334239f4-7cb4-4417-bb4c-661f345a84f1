import { NextRequest, NextResponse } from "next/server";
import { Role } from "@prisma/client";

import {
  registerSchema,
  validateData,
  rateLimitConfigs,
} from "@/lib/validation";
import {
  withMiddleware,
  withSecurityHeaders,
  withRateLimit,
} from "@/lib/middleware";
import { AuthService } from "@/lib/services/auth";

async function registerHandler(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate input using Zod schema
    const validation = validateData(registerSchema, body);

    if (!validation.success) {
      return NextResponse.json({ error: validation.error }, { status: 400 });
    }

    const { email, password, roles } = validation.data;

    // Use AuthService to register user
    const result = await AuthService.registerUser({
      email,
      password,
      roles: roles as Role[],
    });

    if (!result.success) {
      const statusCode = result.error?.includes("already exists") ? 409 : 400;

      return NextResponse.json({ error: result.error }, { status: statusCode });
    }

    return NextResponse.json(
      {
        message: result.data.message,
        user: result.data.user,
        emailSent: result.data.emailSent,
      },
      { status: 201 },
    );
  } catch (error) {
    console.error("Registration error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

// Apply middleware
export const POST = withMiddleware(
  withSecurityHeaders(),
  withRateLimit(rateLimitConfigs.auth),
)(registerHandler);

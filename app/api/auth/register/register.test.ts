import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";
import { Role } from "@prisma/client";

import { POST } from "@/app/api/auth/register/route";
import { AuthService } from "@/lib/services/auth";

vi.mock("@/services/auth", () => ({
  AuthService: {
    registerUser: vi.fn(),
  },
}));

describe("/api/auth/register", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should register a new user successfully with email verification", async () => {
    const mockUser = {
      id: "user-123",
      email: "<EMAIL>",
      roles: [Role.MODEL_PROVIDER],
      emailVerified: null,
      createdAt: new Date("2025-06-15T05:20:29.391Z"),
    };

    vi.mocked(AuthService.registerUser).mockResolvedValue({
      success: true,
      data: {
        message:
          "Account created successfully! Please check your email to verify your account before signing in.",
        user: mockUser,
        emailSent: true,
      },
    });

    const request = new NextRequest("http://localhost:3000/api/auth/register", {
      method: "POST",
      body: JSON.stringify({
        email: "<EMAIL>",
        password: "password123",
        roles: ["MODEL_PROVIDER"],
      }),
      headers: {
        "Content-Type": "application/json",
      },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(201);
    expect(data.message).toBe(
      "Account created successfully! Please check your email to verify your account before signing in.",
    );
    expect(data.user).toMatchObject({
      id: mockUser.id,
      email: mockUser.email,
      roles: mockUser.roles,
      emailVerified: mockUser.emailVerified,
    });
    expect(data.user.createdAt).toBeTruthy();
    expect(data.emailSent).toBe(true);

    expect(AuthService.registerUser).toHaveBeenCalledWith({
      email: "<EMAIL>",
      password: "password123",
      roles: ["MODEL_PROVIDER"],
    });
  });

  it("should return 409 if user already exists", async () => {
    vi.mocked(AuthService.registerUser).mockResolvedValue({
      success: false,
      error: "User with this email already exists, please sign in.",
    });

    const request = new NextRequest("http://localhost:3000/api/auth/register", {
      method: "POST",
      body: JSON.stringify({
        email: "<EMAIL>",
        password: "password123",
        roles: ["MODEL_PROVIDER"],
      }),
      headers: {
        "Content-Type": "application/json",
      },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(409);
    expect(data.error).toBe(
      "User with this email already exists, please sign in.",
    );
  });

  it("should handle service errors", async () => {
    vi.mocked(AuthService.registerUser).mockRejectedValue(
      new Error("Service error"),
    );

    const request = new NextRequest("http://localhost:3000/api/auth/register", {
      method: "POST",
      body: JSON.stringify({
        email: "<EMAIL>",
        password: "password123",
        roles: ["MODEL_PROVIDER"],
      }),
      headers: {
        "Content-Type": "application/json",
      },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.error).toBe("Internal server error");
  });

  it("should handle email sending failure gracefully", async () => {
    const mockUser = {
      id: "user-123",
      email: "<EMAIL>",
      roles: [Role.MODEL_PROVIDER],
      emailVerified: null,
      createdAt: new Date("2025-06-15T05:20:29.391Z"),
    };

    vi.mocked(AuthService.registerUser).mockResolvedValue({
      success: true,
      data: {
        message:
          "Account created successfully! Please check your email to verify your account before signing in.",
        user: mockUser,
        emailSent: false,
        emailError: "Email service unavailable",
      },
    });

    const request = new NextRequest("http://localhost:3000/api/auth/register", {
      method: "POST",
      body: JSON.stringify({
        email: "<EMAIL>",
        password: "password123",
        roles: ["MODEL_PROVIDER"],
      }),
      headers: {
        "Content-Type": "application/json",
      },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(201);
    expect(data.message).toBe(
      "Account created successfully! Please check your email to verify your account before signing in.",
    );
    expect(data.emailSent).toBe(false);
  });
});

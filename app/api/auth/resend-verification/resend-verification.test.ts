import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";

import { POST } from "@/app/api/auth/resend-verification/route";
import { prisma } from "@/lib/db";
import { EmailService } from "@/lib/services/email";

vi.mock("@/lib/db", () => ({
  prisma: {
    user: {
      findUnique: vi.fn(),
      update: vi.fn(),
    },
    userToken: {
      create: vi.fn(),
      deleteMany: vi.fn(),
    },
  },
}));

vi.mock("@/lib/email", () => ({
  generateVerificationToken: vi.fn(() => "new-token"),
  getVerificationExpiry: vi.fn(
    () => new Date(Date.now() + 24 * 60 * 60 * 1000),
  ),
}));

vi.mock("@/services/email", () => ({
  EmailService: {
    sendVerificationEmail: vi.fn(),
    resendVerificationEmail: vi.fn(),
  },
}));

describe("/api/auth/resend-verification", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("POST /api/auth/resend-verification", () => {
    it("should resend verification email successfully", async () => {
      const mockUser = {
        id: "user-123",
        email: "<EMAIL>",
        emailVerified: false,
        emailVerificationToken: "old-token",
        emailVerificationExpires: new Date(Date.now() - 60 * 60 * 1000), // Expired
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);
      vi.mocked(EmailService.resendVerificationEmail).mockResolvedValue({
        userId: "user-123",
        token: "new-token",
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
        emailSent: true,
      });

      const request = new NextRequest(
        "http://localhost:3000/api/auth/resend-verification",
        {
          method: "POST",
          body: JSON.stringify({
            email: "<EMAIL>",
          }),
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe(
        "Verification email sent successfully! Please check your email.",
      );
      expect(data.emailSent).toBe(true);

      // The user.update call is no longer expected since we use UserToken now
      // Instead, we should expect the EmailService.resendVerificationEmail to be called
      expect(EmailService.resendVerificationEmail).toHaveBeenCalledWith(
        "<EMAIL>",
      );
    });

    it("should return 400 for missing email", async () => {
      const request = new NextRequest(
        "http://localhost:3000/api/auth/resend-verification",
        {
          method: "POST",
          body: JSON.stringify({}),
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("Invalid email format");
    });

    it("should return 400 for invalid email format", async () => {
      const request = new NextRequest(
        "http://localhost:3000/api/auth/resend-verification",
        {
          method: "POST",
          body: JSON.stringify({
            email: "invalid-email",
          }),
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("Invalid email format");
    });

    it("should return generic message for non-existent user (security)", async () => {
      vi.mocked(prisma.user.findUnique).mockResolvedValue(null);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/resend-verification",
        {
          method: "POST",
          body: JSON.stringify({
            email: "<EMAIL>",
          }),
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe(
        "If an account with this email exists and is not verified, a verification email has been sent.",
      );
    });

    it("should return 400 for already verified email", async () => {
      const mockUser = {
        id: "user-123",
        email: "<EMAIL>",
        emailVerified: true,
        emailVerificationToken: null,
        emailVerificationExpires: null,
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/resend-verification",
        {
          method: "POST",
          body: JSON.stringify({
            email: "<EMAIL>",
          }),
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("Email is already verified");
    });

    it("should return 429 for recent verification email (rate limiting)", async () => {
      const mockUser = {
        id: "user-123",
        email: "<EMAIL>",
        emailVerified: false,
        emailVerificationToken: "recent-token",
        emailVerificationExpires: new Date(Date.now() + 23 * 60 * 60 * 1000), // Recent (within 5 minutes)
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/resend-verification",
        {
          method: "POST",
          body: JSON.stringify({
            email: "<EMAIL>",
          }),
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(429);
      expect(data.error).toBe(
        "A verification email was recently sent. Please wait 30 seconds before requesting another.",
      );
    });

    it("should handle email sending failure", async () => {
      const mockUser = {
        id: "user-123",
        email: "<EMAIL>",
        emailVerified: false,
        emailVerificationToken: "old-token",
        emailVerificationExpires: new Date(Date.now() - 60 * 60 * 1000),
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser as any);
      vi.mocked(EmailService.resendVerificationEmail).mockRejectedValue(
        new Error("Email service unavailable"),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/resend-verification",
        {
          method: "POST",
          body: JSON.stringify({
            email: "<EMAIL>",
          }),
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe(
        "Failed to send verification email. Please try again later.",
      );
    });

    it("should handle database errors", async () => {
      vi.mocked(prisma.user.findUnique).mockRejectedValue(
        new Error("Database error"),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/resend-verification",
        {
          method: "POST",
          body: JSON.stringify({
            email: "<EMAIL>",
          }),
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Internal server error");
    });
  });
});

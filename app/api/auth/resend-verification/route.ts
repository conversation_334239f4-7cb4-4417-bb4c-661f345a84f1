import { NextRequest, NextResponse } from "next/server";

import {
  resendVerificationSchema,
  validateData,
  rateLimitConfigs,
} from "@/lib/validation";
import {
  withMiddleware,
  withSecurityHeaders,
  withRateLimit,
} from "@/lib/middleware";
import { AuthService } from "@/lib/services/auth";

async function resendVerificationHandler(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate input using Zod schema
    const validation = validateData(resendVerificationSchema, body);

    if (!validation.success) {
      return NextResponse.json({ error: validation.error }, { status: 400 });
    }

    const { email } = validation.data;

    // Use AuthService to resend verification email
    const result = await AuthService.resendVerificationEmail(email);

    if (!result.success) {
      // Handle specific error cases
      if (result.error === "User not found") {
        // Don't reveal if user exists or not for security
        return NextResponse.json(
          {
            message:
              "If an account with this email exists and is not verified, a verification email has been sent.",
          },
          { status: 200 },
        );
      }

      if (result.error === "Email is already verified") {
        return NextResponse.json({ error: result.error }, { status: 400 });
      }

      // For other errors, return 500
      return NextResponse.json(
        { error: "Failed to send verification email. Please try again later." },
        { status: 500 },
      );
    }

    return NextResponse.json(
      {
        message: result.data.message,
        emailSent: result.data.emailSent,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error("Resend verification error:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

// Apply middleware
export const POST = withMiddleware(
  withSecurityHeaders(),
  withRateLimit(rateLimitConfigs.auth),
)(resendVerificationHandler);

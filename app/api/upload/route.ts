import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";

import { authOptions } from "@/lib/auth";
import { FileService } from "@/lib/services/file";
import { withApiErrorHandling } from "@/lib/error-handler";

async function uploadFile<PERSON>andler(request: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const formData = await request.formData();
  const file = formData.get("file") as File;

  if (!file) {
    return NextResponse.json({ error: "No file provided" }, { status: 400 });
  }

  // Use FileService to upload file
  const result = await FileService.uploadFile({
    file,
    userId: session.user.id,
  });

  return NextResponse.json(result);
}

// Apply error handling middleware
export const POST = withApiErrorHandling(uploadFileHandler);

export async function GET() {
  return NextResponse.json({
    message: "File Upload API",
    maxSize: "5MB",
    allowedTypes: FileService.getAllowedTypes(),
    usage: {
      method: "POST",
      contentType: "multipart/form-data",
      field: "file",
      authentication: "Required (session)",
    },
  });
}

// app/auth/error/AuthErrorPageContent.tsx
"use client";

import { useSearchParams } from "next/navigation";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { But<PERSON> } from "@heroui/button";
import { Link } from "@heroui/link";

export default function AuthErrorPageContent() {
  const searchParams = useSearchParams();
  const error = searchParams.get("error");

  const getErrorMessage = (errorType: string | null) => {
    switch (errorType) {
      case "Configuration":
        return "There is a problem with the server configuration. Please contact support.";
      case "AccessDenied":
        return "Access was denied. You may have cancelled the sign-in process.";
      case "Verification":
        return "The verification token has expired or has already been used.";
      case "OAuthSignin":
        return "Error in constructing an authorization URL. Please try again.";
      case "OAuthCallback":
        return "Error in handling the response from the OAuth provider.";
      case "OAuthCreateAccount":
        return "Could not create OAuth account in the database.";
      case "EmailCreateAccount":
        return "Could not create email account in the database.";
      case "Callback":
        return "Error in the OAuth callback handler route.";
      case "OAuthAccountNotLinked":
        return "The email on the account is already linked, but not with this OAuth account. Sign in with your original account to link it.";
      case "EmailSignin":
        return "Sending the e-mail with the verification token failed.";
      case "CredentialsSignin":
        return "The authorize callback returned null in the Credentials provider.";
      case "SessionRequired":
        return "The content of this page requires you to be signed in at all times.";
      default:
        return "An unexpected error occurred during authentication. Please try again.";
    }
  };

  const getErrorTitle = (errorType: string | null) => {
    switch (errorType) {
      case "AccessDenied":
        return "Access Denied";
      case "Configuration":
        return "Configuration Error";
      case "Verification":
        return "Verification Failed";
      case "OAuthAccountNotLinked":
        return "Account Not Linked";
      default:
        return "Authentication Error";
    }
  };

  return (
    <div className="flex items-center justify-center min-h-[80vh] py-8">
      <Card className="w-full max-w-md">
        <CardHeader className="flex flex-col gap-3 text-center">
          <div className="text-6xl text-danger">⚠️</div>
          <h1 className="text-2xl font-bold text-danger">
            {getErrorTitle(error)}
          </h1>
        </CardHeader>
        <CardBody>
          <div className="space-y-4">
            <p className="text-center text-default-600">
              {getErrorMessage(error)}
            </p>

            {error === "OAuthAccountNotLinked" && (
              <div className="p-3 text-sm rounded-lg bg-warning/10 text-warning">
                <p className="font-medium mb-1">Account Linking Required</p>
                <p>
                  This email address is already associated with an account.
                  Please sign in with your existing credentials first, then you
                  can link your Google account in your profile settings.
                </p>
              </div>
            )}

            <div className="flex flex-col gap-3">
              <Button
                as={Link}
                className="w-full"
                color="primary"
                href="/login"
              >
                Back to Sign In
              </Button>
              <Button
                as={Link}
                className="w-full"
                color="default"
                href="/register"
                variant="bordered"
              >
                Create New Account
              </Button>
            </div>

            {error && (
              <div className="text-xs text-center text-default-400">
                Error Code: {error}
              </div>
            )}
          </div>
        </CardBody>
      </Card>
    </div>
  );
}
